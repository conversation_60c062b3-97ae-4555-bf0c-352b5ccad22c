# 🚪 门卡片设计规范 - Celra漫画编辑部

## 📐 整体尺寸规范

```
门卡片总尺寸: 280x420pt
圆角半径: 12pt
边框宽度: 4pt (木质纹理)
内边距: 16pt
```

## 🎨 视觉层次结构

### 1. 背景层
```objc
// 毛玻璃效果 + 角色专属颜色覆盖
UIVisualEffectView *blurView = [[UIVisualEffectView alloc] 
    initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleDark]];
blurView.alpha = 0.8;

// 角色专属颜色覆盖层
UIView *colorOverlay = [[UIView alloc] init];
colorOverlay.backgroundColor = [character.doorColor colorWithAlphaComponent:0.3];
```

### 2. 门框设计
```
┌─────────────────────────────────┐
│ 🔍 猫眼区域 (顶部居中)           │
│                                 │
│  ┌─────────────────────────┐    │
│  │                         │    │
│  │     门图片区域           │    │
│  │   (doorimage/door)      │    │ ← 门把手 (右侧)
│  │     200x240pt           │    │
│  │                         │    │
│  └─────────────────────────┘    │
│  ═══════════════════════════    │ ← 金属分割线
│                                 │
│  角色中文名 (20pt粗体)           │
│  EnglishName (14pt常规)         │
│  简介描述文字 (12pt, 2行)        │
│                                 │
│         🔔 敲门按钮              │
│        (60x60pt圆形)            │
└─────────────────────────────────┘
```

## 🔍 猫眼设计规范

### 视觉设计
```objc
// 猫眼外圈 - 金属质感
UIView *peepholeOuter = [[UIView alloc] init];
peepholeOuter.frame = CGRectMake(0, 0, 40, 40);
peepholeOuter.layer.cornerRadius = 20;
peepholeOuter.backgroundColor = [UIColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0];

// 猫眼内圈 - AI状态指示器
UIView *peepholeInner = [[UIView alloc] init];
peepholeInner.frame = CGRectMake(8, 8, 24, 24);
peepholeInner.layer.cornerRadius = 12;
peepholeInner.backgroundColor = [CelraDesignSystem accentColor];

// 脉动动画 - 表示AI在线
CABasicAnimation *pulseAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
pulseAnimation.fromValue = @0.3;
pulseAnimation.toValue = @1.0;
pulseAnimation.duration = 1.5;
pulseAnimation.repeatCount = INFINITY;
pulseAnimation.autoreverses = YES;
[peepholeInner.layer addAnimation:pulseAnimation forKey:@"pulse"];
```

## 🚪 门图片区域

### 布局规范
```
位置: 距离顶部60pt (猫眼下方)
尺寸: 200x240pt
圆角: 8pt
阴影: 偏移(0,4) 模糊8 透明度0.3
```

### 状态变化
```objc
// 默认状态 - 显示门图片
doorImageView.image = [UIImage imageNamed:@"door"];
doorImageView.alpha = 1.0;

// 敲门后状态 - 显示角色头像
[UIView animateWithDuration:0.5 animations:^{
    doorImageView.alpha = 0.0;
    characterImageView.alpha = 1.0;
}];
```

## 🔘 门把手设计

### 位置和样式
```objc
// 门把手位置 - 门图片右侧中央
CGFloat handleX = doorImageView.frame.origin.x + doorImageView.frame.size.width + 8;
CGFloat handleY = doorImageView.center.y - 15;

UIView *doorHandle = [[UIView alloc] init];
doorHandle.frame = CGRectMake(handleX, handleY, 30, 30);
doorHandle.layer.cornerRadius = 15;

// 金属渐变效果
CAGradientLayer *metalGradient = [CAGradientLayer layer];
metalGradient.colors = @[
    (id)[UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0].CGColor,
    (id)[UIColor colorWithRed:0.7 green:0.7 blue:0.7 alpha:1.0].CGColor
];
metalGradient.frame = doorHandle.bounds;
[doorHandle.layer insertSublayer:metalGradient atIndex:0];
```

## 📝 文字信息区域

### 布局规范
```
位置: 门图片下方16pt
宽度: 卡片宽度 - 32pt (左右各16pt边距)
高度: 自适应内容
```

### 文字样式
```objc
// 中文名 - 主要标题
UILabel *chineseNameLabel = [[UILabel alloc] init];
chineseNameLabel.font = [UIFont systemFontOfSize:20 weight:UIFontWeightBold];
chineseNameLabel.textColor = character.accentColor; // 角色专属颜色
chineseNameLabel.textAlignment = NSTextAlignmentCenter;

// 英文名 - 副标题
UILabel *englishNameLabel = [[UILabel alloc] init];
englishNameLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
englishNameLabel.textColor = [CelraDesignSystem textSecondaryColor];
englishNameLabel.textAlignment = NSTextAlignmentCenter;

// 简介描述 - 正文
UILabel *descriptionLabel = [[UILabel alloc] init];
descriptionLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
descriptionLabel.textColor = [CelraDesignSystem textSecondaryColor];
descriptionLabel.textAlignment = NSTextAlignmentCenter;
descriptionLabel.numberOfLines = 2;
descriptionLabel.lineBreakMode = NSLineBreakByTruncatingTail;
```

## 🔔 敲门按钮设计

### 视觉规范
```objc
UIButton *knockButton = [UIButton buttonWithType:UIButtonTypeCustom];
knockButton.frame = CGRectMake(0, 0, 60, 60);
knockButton.layer.cornerRadius = 30;

// 渐变背景
CAGradientLayer *buttonGradient = [CAGradientLayer layer];
buttonGradient.colors = @[
    (id)[CelraDesignSystem accentColor].CGColor,
    (id)[CelraDesignSystem softYellowColor].CGColor
];
buttonGradient.frame = knockButton.bounds;
buttonGradient.cornerRadius = 30;
[knockButton.layer insertSublayer:buttonGradient atIndex:0];

// 门铃图标
[knockButton setTitle:@"🔔" forState:UIControlStateNormal];
knockButton.titleLabel.font = [UIFont systemFontOfSize:24];
```

### 交互动画
```objc
// 点击动画 - 三段式缩放
- (void)knockButtonTapped:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            button.transform = CGAffineTransformMakeScale(1.1, 1.1);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.1 animations:^{
                button.transform = CGAffineTransformIdentity;
            }];
        }];
    }];
}
```

## 🎭 状态变化动画

### 敲门震动效果
```objc
- (void)shakeDoorCard {
    CABasicAnimation *shake = [CABasicAnimation animationWithKeyPath:@"position.x"];
    shake.fromValue = @(self.center.x - 5);
    shake.toValue = @(self.center.x + 5);
    shake.duration = 0.1;
    shake.repeatCount = 3;
    shake.autoreverses = YES;
    [self.layer addAnimation:shake forKey:@"shake"];
}
```

### 开门效果
```objc
- (void)openDoorAnimation {
    // 门图片淡出
    [UIView animateWithDuration:0.3 animations:^{
        self.doorImageView.alpha = 0.0;
        self.doorImageView.transform = CGAffineTransformMakeScale(0.8, 0.8);
    }];
    
    // 角色头像淡入
    [UIView animateWithDuration:0.3 delay:0.2 options:0 animations:^{
        self.characterImageView.alpha = 1.0;
        self.characterImageView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.2 animations:^{
            self.characterImageView.transform = CGAffineTransformIdentity;
        }];
    }];
    
    // 背景色变化
    [UIView animateWithDuration:0.5 animations:^{
        self.backgroundColor = [self.character.doorColor colorWithAlphaComponent:0.5];
    }];
}
```

## 🎨 角色专属定制

### 颜色映射
```objc
- (UIColor *)doorColorForCharacterType:(AICharacterType)type {
    switch (type) {
        case AICharacterTypeRhythmGuide:
            return [CelraDesignSystem characterPinkColor];
        case AICharacterTypeActionFrame:
            return [CelraDesignSystem accentColor];
        case AICharacterTypeSciFiFrame:
            return [CelraDesignSystem comicBlueColor];
        case AICharacterTypeComedyFrame:
            return [CelraDesignSystem characterMintColor];
        default:
            return [CelraDesignSystem warmPurpleColor];
    }
}
```

这个设计规范确保了每个门卡片都有统一的视觉标准，同时保持了角色的个性化特色。
