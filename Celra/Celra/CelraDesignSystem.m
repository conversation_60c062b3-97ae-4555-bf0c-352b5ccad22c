//
//  CelraDesignSystem.m
//  Celra
//
//  Design system implementation
//

#import "CelraDesignSystem.h"

@implementation CelraDesignSystem

// MARK: - Colors - 漫画编辑部主题配色
+ (UIColor *)primaryBackgroundColor {
    // 深邃的紫罗兰色，营造温馨夜晚编辑部氛围
    return [UIColor colorWithRed:0.165 green:0.106 blue:0.239 alpha:1.0]; // #2A1B3D
}

+ (UIColor *)secondaryBackgroundColor {
    // 渐变中间色，编辑部走廊的氛围
    return [UIColor colorWithRed:0.267 green:0.196 blue:0.553 alpha:1.0]; // #44318D
}

+ (UIColor *)accentColor {
    // 温暖的桃橙色，像温馨的灯光
    return [UIColor colorWithRed:1.0 green:0.702 blue:0.278 alpha:1.0]; // #FFB347
}

+ (UIColor *)textPrimaryColor {
    // 温暖金色，主要文字颜色
    return [UIColor colorWithRed:0.957 green:0.816 blue:0.247 alpha:1.0]; // #F4D03F
}

+ (UIColor *)textSecondaryColor {
    // 半透明白色，次要文字
    return [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.8];
}

+ (UIColor *)cardBackgroundColor {
    // 半透明白色毛玻璃效果
    return [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.15];
}

+ (UIColor *)shadowColor {
    return [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.4];
}

// 新增颜色 - 角色专属和装饰色彩
+ (UIColor *)warmPurpleColor {
    // 渐变第三层颜色，用于背景渐变
    return [UIColor colorWithRed:0.482 green:0.408 blue:0.933 alpha:1.0]; // #7B68EE
}

+ (UIColor *)softYellowColor {
    // 温暖金色，与主文字颜色一致
    return [UIColor colorWithRed:0.957 green:0.816 blue:0.247 alpha:1.0]; // #F4D03F
}

+ (UIColor *)comicBlueColor {
    // 天空蓝，用于科技角色装饰
    return [UIColor colorWithRed:0.529 green:0.808 blue:0.922 alpha:1.0]; // #87CEEB
}

// MARK: - 角色专属颜色
+ (UIColor *)characterPinkColor {
    // 柔和粉色，用于女性角色门卡装饰
    return [UIColor colorWithRed:1.0 green:0.714 blue:0.757 alpha:1.0]; // #FFB6C1
}

+ (UIColor *)characterMintColor {
    // 清新薄荷，用于清新角色门卡装饰
    return [UIColor colorWithRed:0.596 green:0.984 blue:0.596 alpha:1.0]; // #98FB98
}

// MARK: - Typography
+ (UIFont *)titleFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:28 weight:UIFontWeightBold];
    } else {
        return [UIFont boldSystemFontOfSize:28];
    }
}

+ (UIFont *)headlineFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:20 weight:UIFontWeightSemibold];
    } else {
        return [UIFont boldSystemFontOfSize:20];
    }
}

+ (UIFont *)bodyFont {
    return [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
}

+ (UIFont *)captionFont {
    return [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
}

+ (UIFont *)buttonFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    } else {
        return [UIFont boldSystemFontOfSize:16];
    }
}

// MARK: - Spacing
+ (CGFloat)spacingXS { return 4.0; }
+ (CGFloat)spacingS { return 8.0; }
+ (CGFloat)spacingM { return 16.0; }
+ (CGFloat)spacingL { return 24.0; }
+ (CGFloat)spacingXL { return 32.0; }
+ (CGFloat)spacingXXL { return 48.0; }

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS { return 8.0; }
+ (CGFloat)cornerRadiusM { return 12.0; }
+ (CGFloat)cornerRadiusL { return 16.0; }
+ (CGFloat)cornerRadiusXL { return 24.0; }

// MARK: - Shadows
+ (NSShadow *)cardShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 4);
    shadow.shadowBlurRadius = 12;
    return shadow;
}

+ (NSShadow *)buttonShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 2);
    shadow.shadowBlurRadius = 8;
    return shadow;
}

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast { return 0.2; }
+ (NSTimeInterval)animationDurationNormal { return 0.3; }
+ (NSTimeInterval)animationDurationSlow { return 0.5; }

// MARK: - Layout Constants - 漫画编辑部布局
+ (CGFloat)tabBarHeight { return 83.0; } // Standard tab bar height + safe area
+ (CGFloat)navigationBarHeight { return 44.0; }
+ (CGFloat)doorCardWidth { return 280.0; } // 适中的门卡片宽度
+ (CGFloat)doorCardHeight { return 420.0; } // 适中的门卡片高度
+ (CGFloat)knockButtonSize { return 60.0; } // 敲门按钮尺寸
+ (CGFloat)voiceNoteWidth { return 100.0; } // 语音速记工具宽度
+ (CGFloat)voiceNoteHeight { return 70.0; } // 语音速记工具高度
+ (CGFloat)actionButtonWidth { return 120.0; } // 动作按钮宽度
+ (CGFloat)actionButtonHeight { return 44.0; } // 动作按钮高度
+ (CGFloat)characterImageSize { return 200.0; } // 角色图片尺寸

@end
