# 🎨 Celra首页UI设计指南

## 设计理念
打造一个温暖、轻松愉快的漫画编辑部氛围，让用户感觉像是在一个充满创意的工作室中探索不同的AI顾问房间。

## 🌈 配色方案

### 主色调
- **主背景色**: `#261F40` - 温暖的深紫蓝色，营造夜晚编辑部的氛围
- **次要背景色**: `#FAF5EB` - 柔和的米色，像纸张的颜色
- **强调色**: `#FF9933` - 活力橙色，像漫画的高光

### 辅助色彩
- **温暖紫色**: `#7359A6` - 用于装饰元素和渐变
- **柔和黄色**: `#FFF2B3` - 像温暖的灯光，用于文字和光效
- **漫画蓝色**: `#4DB3E6` - 用于点缀和特殊元素
- **卡片背景**: `#33264D` - 半透明的温暖紫色

## 🏗️ 布局结构

### 1. 背景层
- **渐变背景**: 从深紫蓝到紫色的多层渐变
- **浮动光点**: 8个缓慢浮动的光点，营造温馨氛围
- **动画效果**: 3-5秒的缓慢浮动循环

### 2. 顶部区域
- **主标题**: "🎨 漫画编辑部" - 32pt粗体，柔和黄色
- **副标题**: "左右滑动探索不同房间，敲门找到你的AI顾问" - 16pt，带透明度

### 3. 语音速记工具
- **位置**: 右上角固定位置
- **尺寸**: 120x80pt
- **背景**: 毛玻璃效果 + 卡片背景色
- **功能**: 一键录音转文字

### 4. 门卡片区域
- **卡片尺寸**: 320x480pt（更大更突出）
- **滚动方式**: 水平分页滚动
- **间距**: 16pt

#### 门卡片设计细节
- **背景**: 毛玻璃效果 + 温暖色彩覆盖层
- **门框**: 木质纹理，带渐变效果
- **门板**: 角色专属颜色 + 装饰性渐变
- **门把手**: 金属质感，带光泽效果
- **猫眼**: 科技感设计，内圈装饰 + AI指示器脉动
- **文字**: 中文名（22pt粗体）+ 英文名（16pt）+ 描述（14pt）

### 5. 敲门按钮
- **尺寸**: 80x80pt圆形按钮
- **背景**: 橙色到紫色的渐变
- **文字**: "🚪 敲门" - 18pt粗体白色
- **动画**: 三段式弹跳效果（缩小→放大→恢复）

## 🎭 交互设计

### 门敲击体验
1. **点击敲门按钮**
   - 播放敲门音效
   - 按钮三段式动画
   - 0.3秒延迟

2. **门开启效果**
   - 播放开门音效
   - 推入聊天界面
   - 平滑过渡动画

### 滑动切换
- **左右滑动**: 门卡片水平移动
- **视觉反馈**: 当前卡片居中高亮
- **音效**: 轻微的滑动音效

### 语音速记
- **录音状态**: 按钮变红，显示录音波形
- **转换完成**: 显示文字预览
- **保存提示**: 弹窗确认保存成功

## 🎨 视觉特效

### 毛玻璃效果
- 使用UIVisualEffectView实现
- 深色模糊效果
- 增强层次感和现代感

### 阴影系统
- **卡片阴影**: 偏移(0,12) 模糊24 透明度0.6
- **按钮阴影**: 偏移(0,6) 模糊12 透明度0.8
- **文字阴影**: 偏移(1,1) 模糊2 透明度0.5

### 动画时长
- **快速动画**: 0.1-0.2秒（按钮反馈）
- **标准动画**: 0.3秒（界面切换）
- **慢速动画**: 3-5秒（环境光效）

## 📱 适配性考虑

### 不同屏幕尺寸
- 卡片尺寸按屏幕宽度自适应
- 最小宽度280pt，最大宽度360pt
- 间距根据屏幕宽度动态调整

### 深色模式兼容
- 当前配色方案已适配深色环境
- 文字对比度符合可访问性标准
- 保持温暖氛围不变

## 🔧 技术实现要点

### 渐变背景
```objc
CAGradientLayer *gradientLayer = [CAGradientLayer layer];
gradientLayer.colors = @[primaryColor, secondaryColor, tertiaryColor];
gradientLayer.startPoint = CGPointMake(0, 0);
gradientLayer.endPoint = CGPointMake(1, 1);
```

### 毛玻璃效果
```objc
UIVisualEffectView *blurView = [[UIVisualEffectView alloc] 
    initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleDark]];
```

### 脉动动画
```objc
CABasicAnimation *pulseAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
pulseAnimation.fromValue = @0.3;
pulseAnimation.toValue = @1.0;
pulseAnimation.duration = 1.5;
pulseAnimation.repeatCount = INFINITY;
pulseAnimation.autoreverses = YES;
```

这个设计方案完美结合了现代UI设计趋势和漫画编辑部的主题，创造出既专业又有趣的用户体验。
