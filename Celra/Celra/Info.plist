<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- 应用基本信息 -->
	<key>CFBundleName</key>
	<string>Celra</string>
	<key>CFBundleDisplayName</key>
	<string>Celra</string>
	<key>CFBundleIdentifier</key>
	<string>com.celra.app</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>

	<!-- 最低系统版本 -->
	<key>UIMinimumOSVersion</key>
	<string>12.0</string>

	<!-- 语音录音权限 -->
	<key>NSMicrophoneUsageDescription</key>
	<string>Celra需要访问麦克风来录制语音笔记，帮助您快速记录创意想法</string>

	<!-- 语音识别权限 -->
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Celra需要使用语音识别功能将您的录音转换为文字，方便您查看和编辑笔记内容</string>

	<!-- 支持的界面方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>

	<!-- iPhone专用界面方向 -->
	<key>UISupportedInterfaceOrientations~iphone</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>

	<!-- 状态栏样式 -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>

	<!-- 启动屏幕 -->
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>

	<!-- 主界面 -->
	<key>UIMainStoryboardFile</key>
	<string>Main</string>

	<!-- 设备类型 -->
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>

	<!-- 需要全屏 -->
	<key>UIRequiresFullScreen</key>
	<true/>
</dict>
</plist>
