//
//  CelraDesignSystem.h
//  Celra
//
//  Design system constants and utilities
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface CelraDesignSystem : NSObject

// MARK: - Colors
+ (UIColor *)primaryBackgroundColor;      // #261F40 - 温暖深紫蓝
+ (UIColor *)secondaryBackgroundColor;    // #FAF5EB - 柔和米色
+ (UIColor *)accentColor;                 // #FF9933 - 活力橙色
+ (UIColor *)textPrimaryColor;            // 主要文字颜色
+ (UIColor *)textSecondaryColor;          // 次要文字颜色
+ (UIColor *)cardBackgroundColor;         // 卡片背景色
+ (UIColor *)shadowColor;                 // 阴影颜色
+ (UIColor *)warmPurpleColor;             // #7359A6 - 温暖紫色
+ (UIColor *)softYellowColor;             // #FFF2B3 - 柔和黄色
+ (UIColor *)comicBlueColor;              // #4DB3E6 - 漫画蓝色

// MARK: - Typography
+ (UIFont *)titleFont;                    // Large titles
+ (UIFont *)headlineFont;                 // Section headers
+ (UIFont *)bodyFont;                     // Regular text
+ (UIFont *)captionFont;                  // Small text
+ (UIFont *)buttonFont;                   // Button text

// MARK: - Spacing
+ (CGFloat)spacingXS;                     // 4pt
+ (CGFloat)spacingS;                      // 8pt
+ (CGFloat)spacingM;                      // 16pt
+ (CGFloat)spacingL;                      // 24pt
+ (CGFloat)spacingXL;                     // 32pt
+ (CGFloat)spacingXXL;                    // 48pt

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS;                 // 8pt
+ (CGFloat)cornerRadiusM;                 // 12pt
+ (CGFloat)cornerRadiusL;                 // 16pt
+ (CGFloat)cornerRadiusXL;                // 24pt

// MARK: - Shadows
+ (NSShadow *)cardShadow;
+ (NSShadow *)buttonShadow;

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast;   // 0.2s
+ (NSTimeInterval)animationDurationNormal; // 0.3s
+ (NSTimeInterval)animationDurationSlow;   // 0.5s

// MARK: - Layout Constants
+ (CGFloat)tabBarHeight;
+ (CGFloat)navigationBarHeight;
+ (CGFloat)doorCardWidth;
+ (CGFloat)doorCardHeight;
+ (CGFloat)knockButtonSize;

@end

NS_ASSUME_NONNULL_END
