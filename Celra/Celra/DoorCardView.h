//
//  DoorCardView.h
//  Celra
//
//  Door card view for AI characters
//

#import <UIKit/UIKit.h>
#import "AICharacter.h"

NS_ASSUME_NONNULL_BEGIN

@class DoorCardView;

@protocol DoorCardViewDelegate <NSObject>
- (void)doorCardView:(DoorCardView *)cardView didTapChatWithCharacter:(<PERSON><PERSON><PERSON><PERSON> *)character;
- (void)doorCardView:(DoorCardView *)cardView didTapDetailsForCharacter:(<PERSON><PERSON>haracter *)character;
@end

typedef NS_ENUM(NSInteger, DoorCardState) {
    DoorCardStateClosed,    // Door is closed
    DoorCardStateOpening,   // Door is opening animation
    DoorCardStateOpen       // Door is open, showing character and buttons
};

@interface DoorCardView : UIView

@property (nonatomic, strong, readonly) <PERSON>Character *character;
@property (nonatomic, assign) DoorCardState state;
@property (nonatomic, weak) id<DoorCardViewDelegate> delegate;

- (instancetype)initWithCharacter:(AICharacter *)character;
- (void)openDoorWithAnimation;
- (void)closeDoorWithAnimation;

@end

NS_ASSUME_NONNULL_END
