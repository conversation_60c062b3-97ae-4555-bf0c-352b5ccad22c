//
//  AppDelegate.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "AppDelegate.h"
#import "MainTabBarController.h"

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Create window for all iOS versions
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];

    // Create the main tab bar controller using our new implementation
    MainTabBarController *tabBarController = [[MainTabBarController alloc] init];

    self.window.rootViewController = tabBarController;
    [self.window makeKeyAndVisible];

    return YES;
}

@end
