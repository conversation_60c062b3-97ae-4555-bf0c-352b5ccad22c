//
//  CelraDesignSystem.h
//  Celra
//
//  Design system constants and utilities
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface CelraDesignSystem : NSObject

// MARK: - Colors - 漫画编辑部主题配色
+ (UIColor *)primaryBackgroundColor;      // #2A1B3D - 深邃紫罗兰
+ (UIColor *)secondaryBackgroundColor;    // #44318D - 渐变中间色
+ (UIColor *)accentColor;                 // #FFB347 - 温暖桃橙色
+ (UIColor *)textPrimaryColor;            // #F4D03F - 温暖金色
+ (UIColor *)textSecondaryColor;          // 半透明白色
+ (UIColor *)cardBackgroundColor;         // 半透明白色毛玻璃
+ (UIColor *)shadowColor;                 // 阴影颜色
+ (UIColor *)warmPurpleColor;             // #7B68EE - 渐变第三层
+ (UIColor *)softYellowColor;             // #F4D03F - 温暖金色
+ (UIColor *)comicBlueColor;              // #87CEEB - 天空蓝
+ (UIColor *)characterPinkColor;          // #FFB6C1 - 柔和粉色
+ (UIColor *)characterMintColor;          // #98FB98 - 清新薄荷

// MARK: - Typography
+ (UIFont *)titleFont;                    // Large titles
+ (UIFont *)headlineFont;                 // Section headers
+ (UIFont *)bodyFont;                     // Regular text
+ (UIFont *)captionFont;                  // Small text
+ (UIFont *)buttonFont;                   // Button text

// MARK: - Spacing
+ (CGFloat)spacingXS;                     // 4pt
+ (CGFloat)spacingS;                      // 8pt
+ (CGFloat)spacingM;                      // 16pt
+ (CGFloat)spacingL;                      // 24pt
+ (CGFloat)spacingXL;                     // 32pt
+ (CGFloat)spacingXXL;                    // 48pt

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS;                 // 8pt
+ (CGFloat)cornerRadiusM;                 // 12pt
+ (CGFloat)cornerRadiusL;                 // 16pt
+ (CGFloat)cornerRadiusXL;                // 24pt

// MARK: - Shadows
+ (NSShadow *)cardShadow;
+ (NSShadow *)buttonShadow;

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast;   // 0.2s
+ (NSTimeInterval)animationDurationNormal; // 0.3s
+ (NSTimeInterval)animationDurationSlow;   // 0.5s

// MARK: - Layout Constants
+ (CGFloat)tabBarHeight;
+ (CGFloat)navigationBarHeight;
+ (CGFloat)doorCardWidth;
+ (CGFloat)doorCardHeight;
+ (CGFloat)knockButtonSize;
+ (CGFloat)voiceNoteWidth;
+ (CGFloat)voiceNoteHeight;
+ (CGFloat)actionButtonWidth;
+ (CGFloat)actionButtonHeight;
+ (CGFloat)characterImageSize;

@end

NS_ASSUME_NONNULL_END
