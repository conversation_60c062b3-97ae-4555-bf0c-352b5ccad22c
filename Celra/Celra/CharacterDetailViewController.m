//
//  CharacterDetailViewController.m
//  Celra
//
//  Character detail view controller implementation
//

#import "CharacterDetailViewController.h"
#import "CelraDesignSystem.h"
#import "ChatViewController.h"

@interface CharacterDetailViewController ()

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;

// Hero section
@property (nonatomic, strong) UIImageView *heroImageView;
@property (nonatomic, strong) UIView *heroOverlayView;
@property (nonatomic, strong) UILabel *heroTitleLabel;
@property (nonatomic, strong) UILabel *heroSubtitleLabel;

// Content sections
@property (nonatomic, strong) UIView *aboutSection;
@property (nonatomic, strong) UILabel *aboutTitleLabel;
@property (nonatomic, strong) UILabel *aboutContentLabel;

@property (nonatomic, strong) UIView *expertiseSection;
@property (nonatomic, strong) UILabel *expertiseTitleLabel;
@property (nonatomic, strong) UILabel *expertiseContentLabel;

// Action button
@property (nonatomic, strong) UIButton *chatButton;

@end

@implementation CharacterDetailViewController

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupConstraints];
    [self populateContent];
}

- (void)setupUI {
    // Background gradient
    self.view.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[
        (id)[CelraDesignSystem primaryBackgroundColor].CGColor,
        (id)[CelraDesignSystem secondaryBackgroundColor].CGColor,
        (id)[CelraDesignSystem warmPurpleColor].CGColor
    ];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 1);
    gradientLayer.frame = self.view.bounds;
    [self.view.layer insertSublayer:gradientLayer atIndex:0];
    
    // Navigation setup
    self.navigationItem.title = @"Character Profile";
    self.navigationController.navigationBar.prefersLargeTitles = NO;
    
    // Close button
    UIBarButtonItem *closeButton = [[UIBarButtonItem alloc] initWithTitle:@"Close" 
                                                                     style:UIBarButtonItemStylePlain 
                                                                    target:self 
                                                                    action:@selector(closeButtonTapped)];
    closeButton.tintColor = [CelraDesignSystem textPrimaryColor];
    self.navigationItem.leftBarButtonItem = closeButton;
    
    // Scroll view
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // Content view
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];
    
    [self setupHeroSection];
    [self setupContentSections];
    [self setupActionButton];
}

- (void)setupHeroSection {
    // Hero image view - magazine style large image
    self.heroImageView = [[UIImageView alloc] init];
    self.heroImageView.image = [UIImage imageNamed:self.character.avatarImageName];
    self.heroImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.heroImageView.clipsToBounds = YES;
    self.heroImageView.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    self.heroImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.heroImageView];
    
    // Hero overlay for text readability
    self.heroOverlayView = [[UIView alloc] init];
    self.heroOverlayView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.4];
    self.heroOverlayView.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    self.heroOverlayView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.heroImageView addSubview:self.heroOverlayView];
    
    // Hero title - character name
    self.heroTitleLabel = [[UILabel alloc] init];
    self.heroTitleLabel.font = [UIFont systemFontOfSize:36 weight:UIFontWeightBold];
    self.heroTitleLabel.textColor = [UIColor whiteColor];
    self.heroTitleLabel.textAlignment = NSTextAlignmentCenter;
    self.heroTitleLabel.numberOfLines = 0;
    self.heroTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.heroOverlayView addSubview:self.heroTitleLabel];
    
    // Hero subtitle - role description
    self.heroSubtitleLabel = [[UILabel alloc] init];
    self.heroSubtitleLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    self.heroSubtitleLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.9];
    self.heroSubtitleLabel.textAlignment = NSTextAlignmentCenter;
    self.heroSubtitleLabel.numberOfLines = 0;
    self.heroSubtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.heroOverlayView addSubview:self.heroSubtitleLabel];
}

- (void)setupContentSections {
    // About section
    self.aboutSection = [self createSectionView];
    [self.contentView addSubview:self.aboutSection];
    
    self.aboutTitleLabel = [self createSectionTitleLabel];
    self.aboutTitleLabel.text = @"About";
    [self.aboutSection addSubview:self.aboutTitleLabel];
    
    self.aboutContentLabel = [self createSectionContentLabel];
    [self.aboutSection addSubview:self.aboutContentLabel];
    
    // Expertise section
    self.expertiseSection = [self createSectionView];
    [self.contentView addSubview:self.expertiseSection];
    
    self.expertiseTitleLabel = [self createSectionTitleLabel];
    self.expertiseTitleLabel.text = @"Expertise";
    [self.expertiseSection addSubview:self.expertiseTitleLabel];
    
    self.expertiseContentLabel = [self createSectionContentLabel];
    [self.expertiseSection addSubview:self.expertiseContentLabel];
}

- (UIView *)createSectionView {
    UIView *sectionView = [[UIView alloc] init];
    sectionView.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    sectionView.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    sectionView.translatesAutoresizingMaskIntoConstraints = NO;
    return sectionView;
}

- (UILabel *)createSectionTitleLabel {
    UILabel *label = [[UILabel alloc] init];
    label.font = [UIFont systemFontOfSize:24 weight:UIFontWeightBold];
    label.textColor = [CelraDesignSystem textPrimaryColor];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (UILabel *)createSectionContentLabel {
    UILabel *label = [[UILabel alloc] init];
    label.font = [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
    label.textColor = [CelraDesignSystem textSecondaryColor];
    label.numberOfLines = 0;
    label.lineBreakMode = NSLineBreakByWordWrapping;
    label.translatesAutoresizingMaskIntoConstraints = NO;
    return label;
}

- (void)setupActionButton {
    self.chatButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.chatButton setTitle:@"Start Chatting" forState:UIControlStateNormal];
    [self.chatButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.chatButton.titleLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightBold];
    self.chatButton.backgroundColor = [CelraDesignSystem accentColor];
    self.chatButton.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    [self.chatButton addTarget:self action:@selector(chatButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.chatButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.chatButton];
}

- (void)populateContent {
    self.heroTitleLabel.text = self.character.englishName;
    self.heroSubtitleLabel.text = self.character.shortDescription;
    self.aboutContentLabel.text = self.character.longDescription;
    self.expertiseContentLabel.text = [self getExpertiseText];
}

- (NSString *)getExpertiseText {
    // Generate expertise text based on character type
    switch (self.character.type) {
        case AICharacterTypeRhythmGuide:
            return @"Specializes in pacing and rhythm for compelling storytelling. Expert in panel timing and flow control.";
        case AICharacterTypeActionFrame:
            return @"Master of dynamic action sequences and movement. Creates engaging fight scenes and chase sequences.";
        case AICharacterTypeDialoguePanel:
            return @"Expert in conversational framing and character dynamics. Specializes in dialogue-heavy scenes.";
        case AICharacterTypeEmotionFrame:
            return @"Focuses on emotional storytelling through visual composition. Expert in mood and atmosphere.";
        case AICharacterTypeComposePro:
            return @"Professional composition advisor. Masters the rule of thirds and visual balance.";
        case AICharacterTypeBeginnerGuide:
            return @"Patient teacher for newcomers. Breaks down complex concepts into easy steps.";
        case AICharacterTypeSciFiFrame:
            return @"Specialist in futuristic and sci-fi storytelling. Expert in world-building and tech visualization.";
        case AICharacterTypeComedyFrame:
            return @"Comedy timing expert. Masters visual gags and humorous panel arrangements.";
        case AICharacterTypeDigitalToolPro:
            return @"Digital workflow specialist. Expert in modern storyboarding tools and techniques.";
        case AICharacterTypeRevisionPro:
            return @"Revision and refinement expert. Helps polish and perfect existing storyboards.";
        default:
            return @"Experienced storyboard advisor ready to help with your creative projects.";
    }
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Content view
        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor],

        // Hero image view - magazine style large hero
        [self.heroImageView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:[CelraDesignSystem spacingL]],
        [self.heroImageView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.heroImageView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-[CelraDesignSystem spacingL]],
        [self.heroImageView.heightAnchor constraintEqualToConstant:400],

        // Hero overlay
        [self.heroOverlayView.topAnchor constraintEqualToAnchor:self.heroImageView.topAnchor],
        [self.heroOverlayView.leadingAnchor constraintEqualToAnchor:self.heroImageView.leadingAnchor],
        [self.heroOverlayView.trailingAnchor constraintEqualToAnchor:self.heroImageView.trailingAnchor],
        [self.heroOverlayView.bottomAnchor constraintEqualToAnchor:self.heroImageView.bottomAnchor],

        // Hero title
        [self.heroTitleLabel.centerXAnchor constraintEqualToAnchor:self.heroOverlayView.centerXAnchor],
        [self.heroTitleLabel.centerYAnchor constraintEqualToAnchor:self.heroOverlayView.centerYAnchor constant:-20],
        [self.heroTitleLabel.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.heroOverlayView.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.heroTitleLabel.trailingAnchor constraintLessThanOrEqualToAnchor:self.heroOverlayView.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // Hero subtitle
        [self.heroSubtitleLabel.topAnchor constraintEqualToAnchor:self.heroTitleLabel.bottomAnchor constant:[CelraDesignSystem spacingS]],
        [self.heroSubtitleLabel.centerXAnchor constraintEqualToAnchor:self.heroOverlayView.centerXAnchor],
        [self.heroSubtitleLabel.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.heroOverlayView.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.heroSubtitleLabel.trailingAnchor constraintLessThanOrEqualToAnchor:self.heroOverlayView.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // About section
        [self.aboutSection.topAnchor constraintEqualToAnchor:self.heroImageView.bottomAnchor constant:[CelraDesignSystem spacingXL]],
        [self.aboutSection.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.aboutSection.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // About title
        [self.aboutTitleLabel.topAnchor constraintEqualToAnchor:self.aboutSection.topAnchor constant:[CelraDesignSystem spacingL]],
        [self.aboutTitleLabel.leadingAnchor constraintEqualToAnchor:self.aboutSection.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.aboutTitleLabel.trailingAnchor constraintEqualToAnchor:self.aboutSection.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // About content
        [self.aboutContentLabel.topAnchor constraintEqualToAnchor:self.aboutTitleLabel.bottomAnchor constant:[CelraDesignSystem spacingM]],
        [self.aboutContentLabel.leadingAnchor constraintEqualToAnchor:self.aboutSection.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.aboutContentLabel.trailingAnchor constraintEqualToAnchor:self.aboutSection.trailingAnchor constant:-[CelraDesignSystem spacingL]],
        [self.aboutContentLabel.bottomAnchor constraintEqualToAnchor:self.aboutSection.bottomAnchor constant:-[CelraDesignSystem spacingL]],

        // Expertise section
        [self.expertiseSection.topAnchor constraintEqualToAnchor:self.aboutSection.bottomAnchor constant:[CelraDesignSystem spacingL]],
        [self.expertiseSection.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.expertiseSection.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // Expertise title
        [self.expertiseTitleLabel.topAnchor constraintEqualToAnchor:self.expertiseSection.topAnchor constant:[CelraDesignSystem spacingL]],
        [self.expertiseTitleLabel.leadingAnchor constraintEqualToAnchor:self.expertiseSection.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.expertiseTitleLabel.trailingAnchor constraintEqualToAnchor:self.expertiseSection.trailingAnchor constant:-[CelraDesignSystem spacingL]],

        // Expertise content
        [self.expertiseContentLabel.topAnchor constraintEqualToAnchor:self.expertiseTitleLabel.bottomAnchor constant:[CelraDesignSystem spacingM]],
        [self.expertiseContentLabel.leadingAnchor constraintEqualToAnchor:self.expertiseSection.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.expertiseContentLabel.trailingAnchor constraintEqualToAnchor:self.expertiseSection.trailingAnchor constant:-[CelraDesignSystem spacingL]],
        [self.expertiseContentLabel.bottomAnchor constraintEqualToAnchor:self.expertiseSection.bottomAnchor constant:-[CelraDesignSystem spacingL]],

        // Chat button
        [self.chatButton.topAnchor constraintEqualToAnchor:self.expertiseSection.bottomAnchor constant:[CelraDesignSystem spacingXL]],
        [self.chatButton.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:[CelraDesignSystem spacingXL]],
        [self.chatButton.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-[CelraDesignSystem spacingXL]],
        [self.chatButton.heightAnchor constraintEqualToConstant:56],
        [self.chatButton.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-[CelraDesignSystem spacingXL]]
    ]];
}

- (void)chatButtonTapped {
    ChatViewController *chatVC = [[ChatViewController alloc] initWithCharacter:self.character];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:chatVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:navController animated:YES completion:nil];
}

@end
