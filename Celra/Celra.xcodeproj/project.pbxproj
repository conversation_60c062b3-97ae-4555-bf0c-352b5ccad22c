// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		DE25BD962E40590000DEF00F /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BD952E40590000DEF00F /* ViewController.m */; };
		DE25BD992E40590000DEF00F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD972E40590000DEF00F /* Main.storyboard */; };
		DE25BD9B2E40590200DEF00F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD9A2E40590200DEF00F /* Assets.xcassets */; };
		DE25BD9E2E40590200DEF00F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */; };
		DE25BDA12E40590200DEF00F /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDA02E40590200DEF00F /* main.m */; };
		DE25BDAB2E40590200DEF00F /* CelraTests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDAA2E40590200DEF00F /* CelraTests.m */; };
		DE25BDB52E40590200DEF00F /* CelraUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDB42E40590200DEF00F /* CelraUITests.m */; };
		DE25BDB72E40590200DEF00F /* CelraUITestsLaunchTests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */; };
		DE25BDCF2E40787600DEF00F /* ChatMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC32E40787500DEF00F /* ChatMessage.m */; };
		DE25BDD02E40787600DEF00F /* ChatMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC42E40787500DEF00F /* ChatMessageCell.m */; };
		DE25BDD12E40787600DEF00F /* HallwayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC62E40787500DEF00F /* HallwayViewController.m */; };
		DE25BDD22E40787600DEF00F /* CelraDesignSystem.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC72E40787500DEF00F /* CelraDesignSystem.m */; };
		DE25BDD32E40787600DEF00F /* ChatViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC82E40787500DEF00F /* ChatViewController.m */; };
		DE25BDD42E40787600DEF00F /* DoorCardView.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDC92E40787500DEF00F /* DoorCardView.m */; };
		DE25BDDB2E40787D00DEF00F /* SoundManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDD52E40787D00DEF00F /* SoundManager.m */; };
		DE25BDDC2E40787D00DEF00F /* MainTabBarController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDD92E40787D00DEF00F /* MainTabBarController.m */; };
		DE25BDDD2E40787D00DEF00F /* ProfileViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDDA2E40787D00DEF00F /* ProfileViewController.m */; };
		DE25BDE42E40788500DEF00F /* VoiceNoteView.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDDE2E40788500DEF00F /* VoiceNoteView.m */; };
		DE25BDE52E40788500DEF00F /* VoiceNote.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDDF2E40788500DEF00F /* VoiceNote.m */; };
		DE25BDE62E40788500DEF00F /* VoiceNoteCell.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDE12E40788500DEF00F /* VoiceNoteCell.m */; };
		DE25BDE92E40788B00DEF00F /* AICharacter.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDE82E40788B00DEF00F /* AICharacter.m */; };
		DE25BDEC2E40923800DEF00F /* CharacterDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDEB2E40923800DEF00F /* CharacterDetailViewController.m */; };
		DE25BDEE2E40953900DEF00F /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDED2E40953900DEF00F /* AppDelegate.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		DE25BDA72E40590200DEF00F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DE25BD832E40590000DEF00F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DE25BD8A2E40590000DEF00F;
			remoteInfo = Celra;
		};
		DE25BDB12E40590200DEF00F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DE25BD832E40590000DEF00F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DE25BD8A2E40590000DEF00F;
			remoteInfo = Celra;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		DE25BD8B2E40590000DEF00F /* Celra.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Celra.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BD8E2E40590000DEF00F /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		DE25BD942E40590000DEF00F /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		DE25BD952E40590000DEF00F /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		DE25BD982E40590000DEF00F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DE25BD9A2E40590200DEF00F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DE25BD9D2E40590200DEF00F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DE25BD9F2E40590200DEF00F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DE25BDA02E40590200DEF00F /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		DE25BDA62E40590200DEF00F /* CelraTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CelraTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BDAA2E40590200DEF00F /* CelraTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraTests.m; sourceTree = "<group>"; };
		DE25BDB02E40590200DEF00F /* CelraUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CelraUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BDB42E40590200DEF00F /* CelraUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraUITests.m; sourceTree = "<group>"; };
		DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraUITestsLaunchTests.m; sourceTree = "<group>"; };
		DE25BDC32E40787500DEF00F /* ChatMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ChatMessage.m; sourceTree = "<group>"; };
		DE25BDC42E40787500DEF00F /* ChatMessageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ChatMessageCell.m; sourceTree = "<group>"; };
		DE25BDC52E40787500DEF00F /* HallwayViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HallwayViewController.h; sourceTree = "<group>"; };
		DE25BDC62E40787500DEF00F /* HallwayViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HallwayViewController.m; sourceTree = "<group>"; };
		DE25BDC72E40787500DEF00F /* CelraDesignSystem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CelraDesignSystem.m; sourceTree = "<group>"; };
		DE25BDC82E40787500DEF00F /* ChatViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ChatViewController.m; sourceTree = "<group>"; };
		DE25BDC92E40787500DEF00F /* DoorCardView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DoorCardView.m; sourceTree = "<group>"; };
		DE25BDCA2E40787500DEF00F /* DoorCardView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DoorCardView.h; sourceTree = "<group>"; };
		DE25BDCB2E40787500DEF00F /* ChatMessageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ChatMessageCell.h; sourceTree = "<group>"; };
		DE25BDCC2E40787500DEF00F /* CelraDesignSystem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CelraDesignSystem.h; sourceTree = "<group>"; };
		DE25BDCD2E40787600DEF00F /* ChatMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ChatMessage.h; sourceTree = "<group>"; };
		DE25BDCE2E40787600DEF00F /* ChatViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ChatViewController.h; sourceTree = "<group>"; };
		DE25BDD52E40787D00DEF00F /* SoundManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SoundManager.m; sourceTree = "<group>"; };
		DE25BDD62E40787D00DEF00F /* MainTabBarController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MainTabBarController.h; sourceTree = "<group>"; };
		DE25BDD72E40787D00DEF00F /* ProfileViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ProfileViewController.h; sourceTree = "<group>"; };
		DE25BDD82E40787D00DEF00F /* SoundManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SoundManager.h; sourceTree = "<group>"; };
		DE25BDD92E40787D00DEF00F /* MainTabBarController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MainTabBarController.m; sourceTree = "<group>"; };
		DE25BDDA2E40787D00DEF00F /* ProfileViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ProfileViewController.m; sourceTree = "<group>"; };
		DE25BDDE2E40788500DEF00F /* VoiceNoteView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VoiceNoteView.m; sourceTree = "<group>"; };
		DE25BDDF2E40788500DEF00F /* VoiceNote.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VoiceNote.m; sourceTree = "<group>"; };
		DE25BDE02E40788500DEF00F /* VoiceNoteCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VoiceNoteCell.h; sourceTree = "<group>"; };
		DE25BDE12E40788500DEF00F /* VoiceNoteCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VoiceNoteCell.m; sourceTree = "<group>"; };
		DE25BDE22E40788500DEF00F /* VoiceNoteView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VoiceNoteView.h; sourceTree = "<group>"; };
		DE25BDE32E40788500DEF00F /* VoiceNote.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VoiceNote.h; sourceTree = "<group>"; };
		DE25BDE72E40788B00DEF00F /* AICharacter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AICharacter.h; sourceTree = "<group>"; };
		DE25BDE82E40788B00DEF00F /* AICharacter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AICharacter.m; sourceTree = "<group>"; };
		DE25BDEA2E40923800DEF00F /* CharacterDetailViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CharacterDetailViewController.h; sourceTree = "<group>"; };
		DE25BDEB2E40923800DEF00F /* CharacterDetailViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CharacterDetailViewController.m; sourceTree = "<group>"; };
		DE25BDED2E40953900DEF00F /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DE25BD882E40590000DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA32E40590200DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAD2E40590200DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DE25BD822E40590000DEF00F = {
			isa = PBXGroup;
			children = (
				DE25BD8D2E40590000DEF00F /* Celra */,
				DE25BDA92E40590200DEF00F /* CelraTests */,
				DE25BDB32E40590200DEF00F /* CelraUITests */,
				DE25BD8C2E40590000DEF00F /* Products */,
			);
			sourceTree = "<group>";
		};
		DE25BD8C2E40590000DEF00F /* Products */ = {
			isa = PBXGroup;
			children = (
				DE25BD8B2E40590000DEF00F /* Celra.app */,
				DE25BDA62E40590200DEF00F /* CelraTests.xctest */,
				DE25BDB02E40590200DEF00F /* CelraUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DE25BD8D2E40590000DEF00F /* Celra */ = {
			isa = PBXGroup;
			children = (
				DE25BDED2E40953900DEF00F /* AppDelegate.m */,
				DE25BDEA2E40923800DEF00F /* CharacterDetailViewController.h */,
				DE25BDEB2E40923800DEF00F /* CharacterDetailViewController.m */,
				DE25BDE72E40788B00DEF00F /* AICharacter.h */,
				DE25BDE82E40788B00DEF00F /* AICharacter.m */,
				DE25BDE32E40788500DEF00F /* VoiceNote.h */,
				DE25BDDF2E40788500DEF00F /* VoiceNote.m */,
				DE25BDE02E40788500DEF00F /* VoiceNoteCell.h */,
				DE25BDE12E40788500DEF00F /* VoiceNoteCell.m */,
				DE25BDE22E40788500DEF00F /* VoiceNoteView.h */,
				DE25BDDE2E40788500DEF00F /* VoiceNoteView.m */,
				DE25BDD62E40787D00DEF00F /* MainTabBarController.h */,
				DE25BDD92E40787D00DEF00F /* MainTabBarController.m */,
				DE25BDD72E40787D00DEF00F /* ProfileViewController.h */,
				DE25BDDA2E40787D00DEF00F /* ProfileViewController.m */,
				DE25BDD82E40787D00DEF00F /* SoundManager.h */,
				DE25BDD52E40787D00DEF00F /* SoundManager.m */,
				DE25BDCC2E40787500DEF00F /* CelraDesignSystem.h */,
				DE25BDC72E40787500DEF00F /* CelraDesignSystem.m */,
				DE25BDCD2E40787600DEF00F /* ChatMessage.h */,
				DE25BDC32E40787500DEF00F /* ChatMessage.m */,
				DE25BDCB2E40787500DEF00F /* ChatMessageCell.h */,
				DE25BDC42E40787500DEF00F /* ChatMessageCell.m */,
				DE25BDCE2E40787600DEF00F /* ChatViewController.h */,
				DE25BDC82E40787500DEF00F /* ChatViewController.m */,
				DE25BDCA2E40787500DEF00F /* DoorCardView.h */,
				DE25BDC92E40787500DEF00F /* DoorCardView.m */,
				DE25BDC52E40787500DEF00F /* HallwayViewController.h */,
				DE25BDC62E40787500DEF00F /* HallwayViewController.m */,
				DE25BD8E2E40590000DEF00F /* AppDelegate.h */,
				DE25BD942E40590000DEF00F /* ViewController.h */,
				DE25BD952E40590000DEF00F /* ViewController.m */,
				DE25BD972E40590000DEF00F /* Main.storyboard */,
				DE25BD9A2E40590200DEF00F /* Assets.xcassets */,
				DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */,
				DE25BD9F2E40590200DEF00F /* Info.plist */,
				DE25BDA02E40590200DEF00F /* main.m */,
			);
			path = Celra;
			sourceTree = "<group>";
		};
		DE25BDA92E40590200DEF00F /* CelraTests */ = {
			isa = PBXGroup;
			children = (
				DE25BDAA2E40590200DEF00F /* CelraTests.m */,
			);
			path = CelraTests;
			sourceTree = "<group>";
		};
		DE25BDB32E40590200DEF00F /* CelraUITests */ = {
			isa = PBXGroup;
			children = (
				DE25BDB42E40590200DEF00F /* CelraUITests.m */,
				DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */,
			);
			path = CelraUITests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DE25BD8A2E40590000DEF00F /* Celra */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDBA2E40590200DEF00F /* Build configuration list for PBXNativeTarget "Celra" */;
			buildPhases = (
				DE25BD872E40590000DEF00F /* Sources */,
				DE25BD882E40590000DEF00F /* Frameworks */,
				DE25BD892E40590000DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Celra;
			productName = Celra;
			productReference = DE25BD8B2E40590000DEF00F /* Celra.app */;
			productType = "com.apple.product-type.application";
		};
		DE25BDA52E40590200DEF00F /* CelraTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDBD2E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraTests" */;
			buildPhases = (
				DE25BDA22E40590200DEF00F /* Sources */,
				DE25BDA32E40590200DEF00F /* Frameworks */,
				DE25BDA42E40590200DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DE25BDA82E40590200DEF00F /* PBXTargetDependency */,
			);
			name = CelraTests;
			productName = CelraTests;
			productReference = DE25BDA62E40590200DEF00F /* CelraTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		DE25BDAF2E40590200DEF00F /* CelraUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDC02E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraUITests" */;
			buildPhases = (
				DE25BDAC2E40590200DEF00F /* Sources */,
				DE25BDAD2E40590200DEF00F /* Frameworks */,
				DE25BDAE2E40590200DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DE25BDB22E40590200DEF00F /* PBXTargetDependency */,
			);
			name = CelraUITests;
			productName = CelraUITests;
			productReference = DE25BDB02E40590200DEF00F /* CelraUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DE25BD832E40590000DEF00F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DE25BD8A2E40590000DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
					};
					DE25BDA52E40590200DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = DE25BD8A2E40590000DEF00F;
					};
					DE25BDAF2E40590200DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = DE25BD8A2E40590000DEF00F;
					};
				};
			};
			buildConfigurationList = DE25BD862E40590000DEF00F /* Build configuration list for PBXProject "Celra" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DE25BD822E40590000DEF00F;
			productRefGroup = DE25BD8C2E40590000DEF00F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DE25BD8A2E40590000DEF00F /* Celra */,
				DE25BDA52E40590200DEF00F /* CelraTests */,
				DE25BDAF2E40590200DEF00F /* CelraUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DE25BD892E40590000DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BD9E2E40590200DEF00F /* LaunchScreen.storyboard in Resources */,
				DE25BD9B2E40590200DEF00F /* Assets.xcassets in Resources */,
				DE25BD992E40590000DEF00F /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA42E40590200DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAE2E40590200DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DE25BD872E40590000DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BDD22E40787600DEF00F /* CelraDesignSystem.m in Sources */,
				DE25BD962E40590000DEF00F /* ViewController.m in Sources */,
				DE25BDE42E40788500DEF00F /* VoiceNoteView.m in Sources */,
				DE25BDD02E40787600DEF00F /* ChatMessageCell.m in Sources */,
				DE25BDE62E40788500DEF00F /* VoiceNoteCell.m in Sources */,
				DE25BDDB2E40787D00DEF00F /* SoundManager.m in Sources */,
				DE25BDEC2E40923800DEF00F /* CharacterDetailViewController.m in Sources */,
				DE25BDCF2E40787600DEF00F /* ChatMessage.m in Sources */,
				DE25BDE52E40788500DEF00F /* VoiceNote.m in Sources */,
				DE25BDDC2E40787D00DEF00F /* MainTabBarController.m in Sources */,
				DE25BDD42E40787600DEF00F /* DoorCardView.m in Sources */,
				DE25BDA12E40590200DEF00F /* main.m in Sources */,
				DE25BDD32E40787600DEF00F /* ChatViewController.m in Sources */,
				DE25BDDD2E40787D00DEF00F /* ProfileViewController.m in Sources */,
				DE25BDEE2E40953900DEF00F /* AppDelegate.m in Sources */,
				DE25BDD12E40787600DEF00F /* HallwayViewController.m in Sources */,
				DE25BDE92E40788B00DEF00F /* AICharacter.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA22E40590200DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BDAB2E40590200DEF00F /* CelraTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAC2E40590200DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BDB72E40590200DEF00F /* CelraUITestsLaunchTests.m in Sources */,
				DE25BDB52E40590200DEF00F /* CelraUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		DE25BDA82E40590200DEF00F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DE25BD8A2E40590000DEF00F /* Celra */;
			targetProxy = DE25BDA72E40590200DEF00F /* PBXContainerItemProxy */;
		};
		DE25BDB22E40590200DEF00F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DE25BD8A2E40590000DEF00F /* Celra */;
			targetProxy = DE25BDB12E40590200DEF00F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		DE25BD972E40590000DEF00F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE25BD982E40590000DEF00F /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE25BD9D2E40590200DEF00F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DE25BDB82E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		DE25BDB92E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DE25BDBB2E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 995HYU84B7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Celra/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Celra;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Celra needs microphone access to convert your voice notes to text for storyboard planning.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "Celra uses speech recognition to help you quickly capture storyboard ideas through voice notes.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = duckeggkaifaProfile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DE25BDBC2E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 995HYU84B7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Celra/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Celra;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Celra needs microphone access to convert your voice notes to text for storyboard planning.";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "Celra uses speech recognition to help you quickly capture storyboard ideas through voice notes.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = duckeggkaifaProfile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DE25BDBE2E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 995HYU84B7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = duckeggkaifaProfile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Celra.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Celra";
			};
			name = Debug;
		};
		DE25BDBF2E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 995HYU84B7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = duckeggkaifaProfile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Celra.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Celra";
			};
			name = Release;
		};
		DE25BDC12E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = Celra;
			};
			name = Debug;
		};
		DE25BDC22E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = Celra;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DE25BD862E40590000DEF00F /* Build configuration list for PBXProject "Celra" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDB82E40590200DEF00F /* Debug */,
				DE25BDB92E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDBA2E40590200DEF00F /* Build configuration list for PBXNativeTarget "Celra" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDBB2E40590200DEF00F /* Debug */,
				DE25BDBC2E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDBD2E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDBE2E40590200DEF00F /* Debug */,
				DE25BDBF2E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDC02E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDC12E40590200DEF00F /* Debug */,
				DE25BDC22E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DE25BD832E40590000DEF00F /* Project object */;
}
