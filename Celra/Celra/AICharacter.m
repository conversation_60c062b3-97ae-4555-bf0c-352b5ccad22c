//
//  AICharacter.m
//  Celra
//
//  AI Character model implementation
//

#import "AICharacter.h"

@implementation AICharacter

+ (instancetype)characterWithType:(AICharacterType)type {
    AICharacter *character = [[AICharacter alloc] init];
    character.type = type;
    
    switch (type) {
        case AICharacterTypeRhythmGuide:
            character.chineseName = @"叙事节奏顾问";
            character.englishName = @"RhythmGuide";
            character.shortDescription = @"Design storyboards with balanced pacing.";
            character.longDescription = @"Use wide shots for setup, close-ups for emotion. Vary panel sizes—small for speed, large for pause. Keep 3-5 panels per page for flow.";
            character.doorColor = [UIColor colorWithRed:0.2 green:0.3 blue:0.6 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:1.0 green:0.84 blue:0.0 alpha:1.0];
            break;
            
        case AICharacterTypeActionFrame:
            character.chineseName = @"动作分镜师";
            character.englishName = @"ActionFrame";
            character.shortDescription = @"Create dynamic action sequences.";
            character.longDescription = @"Add diagonal lines and blur for movement. Alternate wide shots (full action) and tight shots (impact). Include in-between panels for fluidity.";
            character.doorColor = [UIColor colorWithRed:0.8 green:0.2 blue:0.2 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:1.0 green:0.42 blue:0.42 alpha:1.0];
            break;
            
        case AICharacterTypeDialoguePanel:
            character.chineseName = @"对话分镜师";
            character.englishName = @"DialoguePanel";
            character.shortDescription = @"Frame conversational scenes effectively.";
            character.longDescription = @"Position characters to show dynamics (taller = dominant). Use over-the-shoulder shots; leave space for speech bubbles.";
            character.doorColor = [UIColor colorWithRed:0.3 green:0.7 blue:0.3 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.5 green:0.9 blue:0.5 alpha:1.0];
            break;
            
        case AICharacterTypeEmotionFrame:
            character.chineseName = @"情感分镜顾问";
            character.englishName = @"EmotionFrame";
            character.shortDescription = @"Convey mood through framing.";
            character.longDescription = @"Low angles for awe, high angles for vulnerability. Warm tones for joy, cool for sadness. Slow pacing for melancholy, quick cuts for anxiety.";
            character.doorColor = [UIColor colorWithRed:0.7 green:0.3 blue:0.7 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.9 green:0.5 blue:0.9 alpha:1.0];
            break;
            
        case AICharacterTypeComposePro:
            character.chineseName = @"构图顾问";
            character.englishName = @"ComposePro";
            character.shortDescription = @"Teach strong panel composition.";
            character.longDescription = @"Apply rule of thirds, use leading lines to guide eyes. Balance busy panels with empty space; vary shapes for interest.";
            character.doorColor = [UIColor colorWithRed:0.4 green:0.4 blue:0.8 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.6 green:0.6 blue:1.0 alpha:1.0];
            break;
            
        case AICharacterTypeBeginnerGuide:
            character.chineseName = @"新手分镜师";
            character.englishName = @"BeginnerGuide";
            character.shortDescription = @"Basics for new storyboard artists.";
            character.longDescription = @"Start with thumbnails to map scenes. Prioritize clarity over detail; practice basic poses and use references.";
            character.doorColor = [UIColor colorWithRed:0.9 green:0.6 blue:0.2 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.4 alpha:1.0];
            break;
            
        case AICharacterTypeSciFiFrame:
            character.chineseName = @"科幻分镜师";
            character.englishName = @"SciFiFrame";
            character.shortDescription = @"Storyboard sci-fi settings and elements.";
            character.longDescription = @"Wide shots for futuristic worlds, close-ups for tech details. Contrast dark/light (neon vs. shadows) for drama.";
            character.doorColor = [UIColor colorWithRed:0.1 green:0.8 blue:0.8 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.3 green:1.0 blue:1.0 alpha:1.0];
            break;
            
        case AICharacterTypeComedyFrame:
            character.chineseName = @"幽默分镜顾问";
            character.englishName = @"ComedyFrame";
            character.shortDescription = @"Create comedic timing in frames.";
            character.longDescription = @"Use unexpected panel breaks for punchlines. Exaggerate expressions; small panels for setup, large for gags.";
            character.doorColor = [UIColor colorWithRed:1.0 green:0.7 blue:0.0 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.2 alpha:1.0];
            break;
            
        case AICharacterTypeDigitalToolPro:
            character.chineseName = @"数字工具顾问";
            character.englishName = @"DigitalToolPro";
            character.shortDescription = @"Recommend digital storyboard tools.";
            character.longDescription = @"Use Clip Studio Paint for templates, Procreate for thumbnails. Learn shortcuts; save layers for easy edits.";
            character.doorColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0];
            break;
            
        case AICharacterTypeRevisionPro:
            character.chineseName = @"改稿顾问";
            character.englishName = @"RevisionPro";
            character.shortDescription = @"Refine existing storyboards.";
            character.longDescription = @"Check clarity—ensure actions read easily. Adjust panel order, remove redundancies; strengthen weak angles.";
            character.doorColor = [UIColor colorWithRed:0.6 green:0.2 blue:0.4 alpha:1.0];
            character.accentColor = [UIColor colorWithRed:0.9 green:0.4 blue:0.6 alpha:1.0];
            break;
    }
    
    character.doorImageName = @"door"; // Use the single door image from doorimage folder
    character.avatarImageName = [NSString stringWithFormat:@"%ld", (long)type + 1]; // Use 1, 2, 3... from actor folder
    
    return character;
}

+ (NSArray<AICharacter *> *)allCharacters {
    NSMutableArray *characters = [NSMutableArray array];
    for (NSInteger i = 0; i < 10; i++) {
        [characters addObject:[AICharacter characterWithType:i]];
    }
    return [characters copy];
}

+ (AICharacter *)characterAtIndex:(NSInteger)index {
    if (index >= 0 && index < 10) {
        return [AICharacter characterWithType:index];
    }
    return nil;
}

@end
