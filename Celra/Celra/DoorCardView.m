//
//  DoorCardView.m
//  Celra
//
//  Door card view implementation
//

#import "DoorCardView.h"
#import "CelraDesignSystem.h"

@interface DoorCardView ()

@property (nonatomic, strong) AICharacter *character;

// Door elements
@property (nonatomic, strong) UIImageView *doorImageView;
@property (nonatomic, strong) UIView *doorFrameView;
@property (nonatomic, strong) UIView *doorHandleView;
@property (nonatomic, strong) UIView *peepholeView;

// Character elements
@property (nonatomic, strong) UIImageView *characterImageView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *englishNameLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

// Action buttons
@property (nonatomic, strong) UIButton *chatButton;
@property (nonatomic, strong) UIButton *detailsButton;
@property (nonatomic, strong) UIStackView *buttonStackView;

// Tap gesture
@property (nonatomic, strong) UITapGestureRecognizer *tapGesture;

@end

@implementation DoorCardView

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
        _state = DoorCardStateClosed;
        [self setupUI];
        [self setupConstraints];
        [self setupGestures];
    }
    return self;
}

- (void)setupUI {
    // Card background
    self.backgroundColor = [UIColor clearColor];

    // Add blur effect background
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleDark]];
    blurView.frame = self.bounds;
    blurView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    blurView.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    blurView.clipsToBounds = YES;
    [self addSubview:blurView];

    // Color overlay
    UIView *colorOverlay = [[UIView alloc] init];
    colorOverlay.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    colorOverlay.frame = self.bounds;
    colorOverlay.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    colorOverlay.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    [self addSubview:colorOverlay];

    // Shadow
    self.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    self.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 12);
    self.layer.shadowRadius = 24;
    self.layer.shadowOpacity = 0.6;

    // Door frame view
    self.doorFrameView = [[UIView alloc] init];
    self.doorFrameView.backgroundColor = [UIColor colorWithRed:0.5 green:0.35 blue:0.2 alpha:1.0];
    self.doorFrameView.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    self.doorFrameView.layer.borderWidth = 3;
    self.doorFrameView.layer.borderColor = [UIColor colorWithRed:0.3 green:0.2 blue:0.1 alpha:1.0].CGColor;
    self.doorFrameView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.doorFrameView];

    // Door image view (using doorimage/door asset)
    self.doorImageView = [[UIImageView alloc] init];
    self.doorImageView.image = [UIImage imageNamed:@"door"];
    self.doorImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.doorImageView.clipsToBounds = YES;
    self.doorImageView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.doorImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.doorImageView];

    // Character image view (initially hidden)
    self.characterImageView = [[UIImageView alloc] init];
    self.characterImageView.image = [UIImage imageNamed:self.character.avatarImageName];
    self.characterImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.characterImageView.clipsToBounds = YES;
    self.characterImageView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.characterImageView.alpha = 0.0; // Initially hidden
    self.characterImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.characterImageView];

    // Door handle
    self.doorHandleView = [[UIView alloc] init];
    self.doorHandleView.backgroundColor = [UIColor colorWithRed:0.9 green:0.8 blue:0.4 alpha:1.0];
    self.doorHandleView.layer.cornerRadius = 8;
    self.doorHandleView.layer.borderWidth = 1;
    self.doorHandleView.layer.borderColor = [UIColor colorWithRed:0.7 green:0.6 blue:0.2 alpha:1.0].CGColor;
    self.doorHandleView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.doorHandleView];

    // Peephole
    self.peepholeView = [[UIView alloc] init];
    self.peepholeView.backgroundColor = [UIColor colorWithRed:0.05 green:0.05 blue:0.1 alpha:1.0];
    self.peepholeView.layer.cornerRadius = 12;
    self.peepholeView.layer.borderWidth = 3;
    self.peepholeView.layer.borderColor = [UIColor colorWithRed:0.7 green:0.6 blue:0.3 alpha:1.0].CGColor;
    self.peepholeView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.peepholeView];

    // AI indicator in peephole
    UIView *aiIndicator = [[UIView alloc] init];
    aiIndicator.backgroundColor = self.character.accentColor;
    aiIndicator.layer.cornerRadius = 4;

    // Pulse animation
    CABasicAnimation *pulseAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
    pulseAnimation.fromValue = @0.3;
    pulseAnimation.toValue = @1.0;
    pulseAnimation.duration = 1.5;
    pulseAnimation.repeatCount = INFINITY;
    pulseAnimation.autoreverses = YES;
    [aiIndicator.layer addAnimation:pulseAnimation forKey:@"pulse"];

    aiIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    [self.peepholeView addSubview:aiIndicator];

    [NSLayoutConstraint activateConstraints:@[
        [aiIndicator.centerXAnchor constraintEqualToAnchor:self.peepholeView.centerXAnchor],
        [aiIndicator.centerYAnchor constraintEqualToAnchor:self.peepholeView.centerYAnchor],
        [aiIndicator.widthAnchor constraintEqualToConstant:8],
        [aiIndicator.heightAnchor constraintEqualToConstant:8]
    ]];

    // Character name label (English only as requested)
    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.text = self.character.englishName;
    self.nameLabel.font = [UIFont systemFontOfSize:22 weight:UIFontWeightBold];
    self.nameLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.nameLabel.textAlignment = NSTextAlignmentCenter;
    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.nameLabel];

    // Description label (English)
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.text = self.character.shortDescription;
    self.descriptionLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    self.descriptionLabel.textColor = [CelraDesignSystem textSecondaryColor];
    self.descriptionLabel.textAlignment = NSTextAlignmentCenter;
    self.descriptionLabel.numberOfLines = 2;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.descriptionLabel];

    // Action buttons (initially hidden)
    [self setupActionButtons];
}

- (void)setupActionButtons {
    // Chat button
    self.chatButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.chatButton setTitle:@"Chat" forState:UIControlStateNormal];
    [self.chatButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.chatButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.chatButton.backgroundColor = [CelraDesignSystem accentColor];
    self.chatButton.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    [self.chatButton addTarget:self action:@selector(chatButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.chatButton.alpha = 0.0; // Initially hidden
    self.chatButton.translatesAutoresizingMaskIntoConstraints = NO;

    // Details button
    self.detailsButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.detailsButton setTitle:@"Details" forState:UIControlStateNormal];
    [self.detailsButton setTitleColor:[CelraDesignSystem textPrimaryColor] forState:UIControlStateNormal];
    self.detailsButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.detailsButton.backgroundColor = [UIColor clearColor];
    self.detailsButton.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.detailsButton.layer.borderWidth = 2;
    self.detailsButton.layer.borderColor = [CelraDesignSystem textPrimaryColor].CGColor;
    [self.detailsButton addTarget:self action:@selector(detailsButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.detailsButton.alpha = 0.0; // Initially hidden
    self.detailsButton.translatesAutoresizingMaskIntoConstraints = NO;

    // Stack view for buttons
    self.buttonStackView = [[UIStackView alloc] initWithArrangedSubviews:@[self.chatButton, self.detailsButton]];
    self.buttonStackView.axis = UILayoutConstraintAxisHorizontal;
    self.buttonStackView.distribution = UIStackViewDistributionFillEqually;
    self.buttonStackView.spacing = [CelraDesignSystem spacingM];
    self.buttonStackView.alpha = 0.0; // Initially hidden
    self.buttonStackView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.buttonStackView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Door frame
        [self.doorFrameView.topAnchor constraintEqualToAnchor:self.topAnchor constant:[CelraDesignSystem spacingL]],
        [self.doorFrameView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.doorFrameView.widthAnchor constraintEqualToConstant:220],
        [self.doorFrameView.heightAnchor constraintEqualToConstant:280],

        // Door image view
        [self.doorImageView.topAnchor constraintEqualToAnchor:self.doorFrameView.topAnchor constant:[CelraDesignSystem spacingS]],
        [self.doorImageView.leadingAnchor constraintEqualToAnchor:self.doorFrameView.leadingAnchor constant:[CelraDesignSystem spacingS]],
        [self.doorImageView.trailingAnchor constraintEqualToAnchor:self.doorFrameView.trailingAnchor constant:-[CelraDesignSystem spacingS]],
        [self.doorImageView.bottomAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor constant:-[CelraDesignSystem spacingS]],

        // Character image view (same position as door image)
        [self.characterImageView.topAnchor constraintEqualToAnchor:self.doorImageView.topAnchor],
        [self.characterImageView.leadingAnchor constraintEqualToAnchor:self.doorImageView.leadingAnchor],
        [self.characterImageView.trailingAnchor constraintEqualToAnchor:self.doorImageView.trailingAnchor],
        [self.characterImageView.bottomAnchor constraintEqualToAnchor:self.doorImageView.bottomAnchor],

        // Door handle
        [self.doorHandleView.trailingAnchor constraintEqualToAnchor:self.doorFrameView.trailingAnchor constant:-[CelraDesignSystem spacingL]],
        [self.doorHandleView.centerYAnchor constraintEqualToAnchor:self.doorFrameView.centerYAnchor],
        [self.doorHandleView.widthAnchor constraintEqualToConstant:16],
        [self.doorHandleView.heightAnchor constraintEqualToConstant:32],

        // Peephole
        [self.peepholeView.centerXAnchor constraintEqualToAnchor:self.doorFrameView.centerXAnchor],
        [self.peepholeView.topAnchor constraintEqualToAnchor:self.doorFrameView.topAnchor constant:[CelraDesignSystem spacingXL]],
        [self.peepholeView.widthAnchor constraintEqualToConstant:24],
        [self.peepholeView.heightAnchor constraintEqualToConstant:24],

        // Name label
        [self.nameLabel.topAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor constant:[CelraDesignSystem spacingM]],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:[CelraDesignSystem spacingS]],
        [self.nameLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-[CelraDesignSystem spacingS]],

        // Description label
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-[CelraDesignSystem spacingS]],

        // Button stack view
        [self.buttonStackView.topAnchor constraintEqualToAnchor:self.descriptionLabel.bottomAnchor constant:[CelraDesignSystem spacingL]],
        [self.buttonStackView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:[CelraDesignSystem spacingL]],
        [self.buttonStackView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-[CelraDesignSystem spacingL]],
        [self.buttonStackView.bottomAnchor constraintLessThanOrEqualToAnchor:self.bottomAnchor constant:-[CelraDesignSystem spacingM]],

        // Button heights
        [self.chatButton.heightAnchor constraintEqualToConstant:[CelraDesignSystem actionButtonHeight]],
        [self.detailsButton.heightAnchor constraintEqualToConstant:[CelraDesignSystem actionButtonHeight]]
    ]];
}

- (void)setupGestures {
    self.tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(doorTapped)];
    [self addGestureRecognizer:self.tapGesture];
}

- (void)doorTapped {
    if (self.state == DoorCardStateClosed) {
        [self openDoorWithAnimation];
    }
}

- (void)openDoorWithAnimation {
    if (self.state != DoorCardStateClosed) return;

    self.state = DoorCardStateOpening;

    // Disable tap gesture during animation
    self.tapGesture.enabled = NO;

    // Door sliding left animation
    [UIView animateWithDuration:0.5 delay:0.0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        // Move door image to the left
        self.doorImageView.transform = CGAffineTransformMakeTranslation(-100, 0);
        self.doorImageView.alpha = 0.3;

        // Show character image
        self.characterImageView.alpha = 1.0;
        self.characterImageView.transform = CGAffineTransformMakeTranslation(-20, 0);

    } completion:^(BOOL finished) {
        // Show action buttons
        [UIView animateWithDuration:0.3 animations:^{
            self.buttonStackView.alpha = 1.0;
            self.buttonStackView.transform = CGAffineTransformMakeTranslation(0, -10);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.2 animations:^{
                self.buttonStackView.transform = CGAffineTransformIdentity;
            }];
            self.state = DoorCardStateOpen;
        }];
    }];
}

- (void)closeDoorWithAnimation {
    if (self.state != DoorCardStateOpen) return;

    self.state = DoorCardStateOpening;

    // Hide action buttons first
    [UIView animateWithDuration:0.2 animations:^{
        self.buttonStackView.alpha = 0.0;
    } completion:^(BOOL finished) {
        // Door sliding back animation
        [UIView animateWithDuration:0.4 delay:0.0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            // Move door image back
            self.doorImageView.transform = CGAffineTransformIdentity;
            self.doorImageView.alpha = 1.0;

            // Hide character image
            self.characterImageView.alpha = 0.0;
            self.characterImageView.transform = CGAffineTransformIdentity;

        } completion:^(BOOL finished) {
            self.state = DoorCardStateClosed;
            self.tapGesture.enabled = YES;
        }];
    }];
}

- (void)chatButtonTapped {
    if (self.delegate && [self.delegate respondsToSelector:@selector(doorCardView:didTapChatWithCharacter:)]) {
        [self.delegate doorCardView:self didTapChatWithCharacter:self.character];
    }
}

- (void)detailsButtonTapped {
    if (self.delegate && [self.delegate respondsToSelector:@selector(doorCardView:didTapDetailsForCharacter:)]) {
        [self.delegate doorCardView:self didTapDetailsForCharacter:self.character];
    }
}

@end
