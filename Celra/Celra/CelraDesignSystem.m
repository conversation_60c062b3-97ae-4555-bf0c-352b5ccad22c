//
//  CelraDesignSystem.m
//  Celra
//
//  Design system implementation
//

#import "CelraDesignSystem.h"

@implementation CelraDesignSystem

// MARK: - Colors
+ (UIColor *)primaryBackgroundColor {
    // 温暖的深紫蓝色，像夜晚的编辑部
    return [UIColor colorWithRed:0.15 green:0.12 blue:0.25 alpha:1.0]; // #261F40
}

+ (UIColor *)secondaryBackgroundColor {
    // 柔和的米色，像纸张的颜色
    return [UIColor colorWithRed:0.98 green:0.96 blue:0.92 alpha:1.0]; // #FAF5EB
}

+ (UIColor *)accentColor {
    // 活力橙色，像漫画的高光
    return [UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0]; // #FF9933
}

+ (UIColor *)textPrimaryColor {
    return [UIColor whiteColor];
}

+ (UIColor *)textSecondaryColor {
    return [UIColor colorWithRed:0.3 green:0.25 blue:0.4 alpha:1.0]; // #4C3F66
}

+ (UIColor *)cardBackgroundColor {
    // 半透明的温暖紫色
    return [UIColor colorWithRed:0.2 green:0.15 blue:0.3 alpha:0.85]; // #33264D
}

+ (UIColor *)shadowColor {
    return [UIColor colorWithRed:0.1 green:0.05 blue:0.15 alpha:0.4];
}

// 新增颜色
+ (UIColor *)warmPurpleColor {
    // 温暖紫色，用于装饰元素
    return [UIColor colorWithRed:0.45 green:0.35 blue:0.65 alpha:1.0]; // #7359A6
}

+ (UIColor *)softYellowColor {
    // 柔和黄色，像温暖的灯光
    return [UIColor colorWithRed:1.0 green:0.95 blue:0.7 alpha:1.0]; // #FFF2B3
}

+ (UIColor *)comicBlueColor {
    // 漫画蓝色，用于点缀
    return [UIColor colorWithRed:0.3 green:0.7 blue:0.9 alpha:1.0]; // #4DB3E6
}

// MARK: - Typography
+ (UIFont *)titleFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:28 weight:UIFontWeightBold];
    } else {
        return [UIFont boldSystemFontOfSize:28];
    }
}

+ (UIFont *)headlineFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:20 weight:UIFontWeightSemibold];
    } else {
        return [UIFont boldSystemFontOfSize:20];
    }
}

+ (UIFont *)bodyFont {
    return [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
}

+ (UIFont *)captionFont {
    return [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
}

+ (UIFont *)buttonFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    } else {
        return [UIFont boldSystemFontOfSize:16];
    }
}

// MARK: - Spacing
+ (CGFloat)spacingXS { return 4.0; }
+ (CGFloat)spacingS { return 8.0; }
+ (CGFloat)spacingM { return 16.0; }
+ (CGFloat)spacingL { return 24.0; }
+ (CGFloat)spacingXL { return 32.0; }
+ (CGFloat)spacingXXL { return 48.0; }

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS { return 8.0; }
+ (CGFloat)cornerRadiusM { return 12.0; }
+ (CGFloat)cornerRadiusL { return 16.0; }
+ (CGFloat)cornerRadiusXL { return 24.0; }

// MARK: - Shadows
+ (NSShadow *)cardShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 4);
    shadow.shadowBlurRadius = 12;
    return shadow;
}

+ (NSShadow *)buttonShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 2);
    shadow.shadowBlurRadius = 8;
    return shadow;
}

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast { return 0.2; }
+ (NSTimeInterval)animationDurationNormal { return 0.3; }
+ (NSTimeInterval)animationDurationSlow { return 0.5; }

// MARK: - Layout Constants
+ (CGFloat)tabBarHeight { return 83.0; } // Standard tab bar height + safe area
+ (CGFloat)navigationBarHeight { return 44.0; }
+ (CGFloat)doorCardWidth { return 320.0; } // 更大的卡片宽度
+ (CGFloat)doorCardHeight { return 480.0; } // 更大的卡片高度
+ (CGFloat)knockButtonSize { return 80.0; } // 更大的敲门按钮

@end
