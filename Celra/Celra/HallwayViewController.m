//
//  HallwayViewController.m
//  Celra
//
//  Hallway view controller implementation
//

#import "HallwayViewController.h"
#import "CelraDesignSystem.h"
#import "AICharacter.h"
#import "DoorCardView.h"
#import "ChatViewController.h"
#import "CharacterDetailViewController.h"
#import "SoundManager.h"
#import "VoiceNoteView.h"

@interface HallwayViewController () <UIScrollViewDelegate, VoiceNoteViewDelegate, DoorCardViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) NSArray<AICharacter *> *characters;
@property (nonatomic, strong) NSMutableArray<DoorCardView *> *doorCards;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, strong) UIButton *knockButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) VoiceNoteView *voiceNoteView;
@property (nonatomic, strong) UIView *voiceNoteContainer;

@end

@implementation HallwayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupData];
    [self setupUI];
    [self setupConstraints];
}

- (void)setupData {
    self.characters = [AICharacter allCharacters];
    self.doorCards = [NSMutableArray array];
    self.currentIndex = 0;
}

- (void)setupGradientBackground {
    // 创建渐变背景层，模拟编辑部的温暖夜晚氛围
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = self.view.bounds;
    gradientLayer.colors = @[
        (id)[CelraDesignSystem primaryBackgroundColor].CGColor,
        (id)[UIColor colorWithRed:0.2 green:0.15 blue:0.35 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.15 green:0.1 blue:0.25 alpha:1.0].CGColor
    ];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 1);
    [self.view.layer insertSublayer:gradientLayer atIndex:0];

    // 添加一些装饰性的光点效果
    [self addFloatingLights];
}

- (void)addFloatingLights {
    // 添加一些浮动的光点，营造温馨的编辑部氛围
    for (int i = 0; i < 8; i++) {
        UIView *lightView = [[UIView alloc] init];
        lightView.backgroundColor = [CelraDesignSystem softYellowColor];
        lightView.alpha = 0.3;
        lightView.layer.cornerRadius = 3;

        CGFloat x = arc4random_uniform(self.view.frame.size.width);
        CGFloat y = arc4random_uniform(self.view.frame.size.height);
        lightView.frame = CGRectMake(x, y, 6, 6);

        [self.view addSubview:lightView];

        // 添加缓慢的浮动动画
        [self animateFloatingLight:lightView];
    }
}

- (void)setupVoiceNoteView {
    // 语音速记容器 - 位于右上角的小工具
    self.voiceNoteContainer = [[UIView alloc] init];
    self.voiceNoteContainer.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.voiceNoteContainer.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    self.voiceNoteContainer.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.voiceNoteContainer.layer.shadowOffset = CGSizeMake(0, 4);
    self.voiceNoteContainer.layer.shadowRadius = 8;
    self.voiceNoteContainer.layer.shadowOpacity = 0.6;
    self.voiceNoteContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.voiceNoteContainer];

    // 语音速记视图
    self.voiceNoteView = [[VoiceNoteView alloc] init];
    self.voiceNoteView.delegate = self;
    self.voiceNoteView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.voiceNoteContainer addSubview:self.voiceNoteView];

    // 添加标题标签 - English interface
    UILabel *voiceLabel = [[UILabel alloc] init];
    voiceLabel.text = @"🎤 Notes";
    voiceLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
    voiceLabel.textColor = [CelraDesignSystem textPrimaryColor];
    voiceLabel.textAlignment = NSTextAlignmentCenter;
    voiceLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.voiceNoteContainer addSubview:voiceLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 容器约束
        [self.voiceNoteContainer.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor
                                                           constant:[CelraDesignSystem spacingM]],
        [self.voiceNoteContainer.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor
                                                               constant:-[CelraDesignSystem spacingM]],
        [self.voiceNoteContainer.widthAnchor constraintEqualToConstant:120],
        [self.voiceNoteContainer.heightAnchor constraintEqualToConstant:80],

        // 标签约束
        [voiceLabel.topAnchor constraintEqualToAnchor:self.voiceNoteContainer.topAnchor
                                             constant:[CelraDesignSystem spacingS]],
        [voiceLabel.leadingAnchor constraintEqualToAnchor:self.voiceNoteContainer.leadingAnchor
                                                 constant:[CelraDesignSystem spacingS]],
        [voiceLabel.trailingAnchor constraintEqualToAnchor:self.voiceNoteContainer.trailingAnchor
                                                  constant:-[CelraDesignSystem spacingS]],

        // 语音视图约束
        [self.voiceNoteView.topAnchor constraintEqualToAnchor:voiceLabel.bottomAnchor
                                                     constant:[CelraDesignSystem spacingXS]],
        [self.voiceNoteView.leadingAnchor constraintEqualToAnchor:self.voiceNoteContainer.leadingAnchor
                                                         constant:[CelraDesignSystem spacingS]],
        [self.voiceNoteView.trailingAnchor constraintEqualToAnchor:self.voiceNoteContainer.trailingAnchor
                                                          constant:-[CelraDesignSystem spacingS]],
        [self.voiceNoteView.bottomAnchor constraintEqualToAnchor:self.voiceNoteContainer.bottomAnchor
                                                        constant:-[CelraDesignSystem spacingS]]
    ]];
}

- (void)setupUI {
    // 创建渐变背景，模拟编辑部的温暖氛围
    [self setupGradientBackground];

    self.navigationItem.title = @"Celra Studio";

    // 主标题 - English interface
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"🎨 Comic Studio Hallway";
    self.titleLabel.font = [UIFont systemFontOfSize:28 weight:UIFontWeightBold];
    self.titleLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.titleLabel];

    // 副标题 - English interface
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.text = @"Swipe to explore rooms, tap doors to meet your AI creative partners";
    self.subtitleLabel.font = [CelraDesignSystem bodyFont];
    self.subtitleLabel.textColor = [CelraDesignSystem textSecondaryColor];
    self.subtitleLabel.textAlignment = NSTextAlignmentCenter;
    self.subtitleLabel.numberOfLines = 2;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.subtitleLabel];
    
    // Scroll view for door cards
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.delegate = self;
    self.scrollView.pagingEnabled = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // Create door cards
    [self createDoorCards];

    // 语音速记容器
    [self setupVoiceNoteView];

    [self updateCurrentCharacterInfo];
}

- (void)createDoorCards {
    CGFloat cardWidth = [CelraDesignSystem doorCardWidth];
    CGFloat spacing = [CelraDesignSystem spacingM];

    for (NSInteger i = 0; i < self.characters.count; i++) {
        AICharacter *character = self.characters[i];
        DoorCardView *doorCard = [[DoorCardView alloc] initWithCharacter:character];
        doorCard.delegate = self; // Set delegate for new interaction flow
        doorCard.translatesAutoresizingMaskIntoConstraints = NO;
        [self.scrollView addSubview:doorCard];
        [self.doorCards addObject:doorCard];

        // Position the door card
        [NSLayoutConstraint activateConstraints:@[
            [doorCard.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor
                                                   constant:i * (cardWidth + spacing) + spacing],
            [doorCard.centerYAnchor constraintEqualToAnchor:self.scrollView.centerYAnchor],
            [doorCard.widthAnchor constraintEqualToConstant:cardWidth],
            [doorCard.heightAnchor constraintEqualToConstant:[CelraDesignSystem doorCardHeight]]
        ]];
    }

    // Set scroll view content size
    CGFloat contentWidth = self.characters.count * (cardWidth + spacing) + spacing;
    self.scrollView.contentSize = CGSizeMake(contentWidth, 0);
}

- (void)setupConstraints {
    CGFloat safeAreaTop = 0;
    if (@available(iOS 11.0, *)) {
        safeAreaTop = self.view.safeAreaInsets.top;
    }
    
    [NSLayoutConstraint activateConstraints:@[
        // Title label
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor 
                                                  constant:[CelraDesignSystem spacingL]],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor 
                                                      constant:[CelraDesignSystem spacingM]],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor 
                                                       constant:-[CelraDesignSystem spacingM]],
        
        // Subtitle label
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor 
                                                         constant:[CelraDesignSystem spacingM]],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor 
                                                          constant:-[CelraDesignSystem spacingM]],
        
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.subtitleLabel.bottomAnchor
                                                  constant:[CelraDesignSystem spacingXL]],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.heightAnchor constraintEqualToConstant:[CelraDesignSystem doorCardHeight]]
    ]];
}

- (void)updateCurrentCharacterInfo {
    // Update any additional UI elements based on current character
    // AICharacter *currentCharacter = self.characters[self.currentIndex];
}

#pragma mark - DoorCardViewDelegate

- (void)doorCardView:(DoorCardView *)cardView didTapChatWithCharacter:(AICharacter *)character {
    // Navigate to chat view controller
    ChatViewController *chatVC = [[ChatViewController alloc] initWithCharacter:character];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:chatVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:navController animated:YES completion:nil];
}

- (void)doorCardView:(DoorCardView *)cardView didTapDetailsForCharacter:(AICharacter *)character {
    // Navigate to character detail view controller
    CharacterDetailViewController *detailVC = [[CharacterDetailViewController alloc] initWithCharacter:character];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:detailVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:navController animated:YES completion:nil];
}

- (void)animateFloatingLight:(UIView *)lightView {
    // 创建缓慢的浮动动画
    [UIView animateWithDuration:3.0 + arc4random_uniform(2)
                          delay:arc4random_uniform(2)
                        options:UIViewAnimationOptionRepeat | UIViewAnimationOptionAutoreverse | UIViewAnimationOptionCurveEaseInOut
                     animations:^{
        CGFloat deltaX = (arc4random_uniform(40) - 20);
        CGFloat deltaY = (arc4random_uniform(40) - 20);
        lightView.transform = CGAffineTransformMakeTranslation(deltaX, deltaY);
        lightView.alpha = 0.1 + (arc4random_uniform(30) / 100.0);
    } completion:nil];
}



#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    CGFloat cardWidth = [CelraDesignSystem doorCardWidth];
    CGFloat spacing = [CelraDesignSystem spacingM];
    CGFloat pageWidth = cardWidth + spacing;
    
    NSInteger newIndex = (NSInteger)(scrollView.contentOffset.x / pageWidth);
    if (newIndex != self.currentIndex && newIndex >= 0 && newIndex < self.characters.count) {
        self.currentIndex = newIndex;
        [self updateCurrentCharacterInfo];
    }
}

#pragma mark - VoiceNoteViewDelegate

- (void)voiceNoteView:(UIView *)view didCreateNote:(VoiceNote *)note {
    // Handle voice note creation
    NSLog(@"Created new voice note: %@", note.text);

    // Add logic to save to local storage or cloud
    // Show a simple confirmation
    [self showVoiceNoteCreatedAlert:note];
}

- (void)showVoiceNoteCreatedAlert:(VoiceNote *)note {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"📝 Voice Note Saved"
                                                                   message:[NSString stringWithFormat:@"Content: %@", note.text]
                                                            preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                       style:UIAlertActionStyleDefault
                                                     handler:nil];
    [alert addAction:okAction];

    [self presentViewController:alert animated:YES completion:nil];
}

@end
