//
//  DoorCardView.m
//  Celra
//
//  Door card view implementation
//

#import "DoorCardView.h"
#import "CelraDesignSystem.h"

@interface DoorCardView ()

@property (nonatomic, strong) AICharacter *character;
@property (nonatomic, strong) UIView *doorView;
@property (nonatomic, strong) UIView *doorFrameView;
@property (nonatomic, strong) UIView *doorHandleView;
@property (nonatomic, strong) UIView *peepholeView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *englishNameLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation DoorCardView

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
        [self setupUI];
        [self setupConstraints];
    }
    return self;
}

- (void)setupUI {
    // 创建更有层次感的卡片背景
    self.backgroundColor = [UIColor clearColor];

    // 添加毛玻璃效果背景
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleDark]];
    blurView.frame = self.bounds;
    blurView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    blurView.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    blurView.clipsToBounds = YES;
    [self addSubview:blurView];

    // 添加温暖的色彩覆盖层
    UIView *colorOverlay = [[UIView alloc] init];
    colorOverlay.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    colorOverlay.frame = self.bounds;
    colorOverlay.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    colorOverlay.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    [self addSubview:colorOverlay];

    // 增强阴影效果
    self.layer.cornerRadius = [CelraDesignSystem cornerRadiusXL];
    self.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 12);
    self.layer.shadowRadius = 24;
    self.layer.shadowOpacity = 0.6;
    
    // Door frame view - 更有质感的木质门框
    self.doorFrameView = [[UIView alloc] init];
    self.doorFrameView.backgroundColor = [UIColor colorWithRed:0.5 green:0.35 blue:0.2 alpha:1.0]; // 更温暖的木色
    self.doorFrameView.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    self.doorFrameView.layer.borderWidth = 3;
    self.doorFrameView.layer.borderColor = [UIColor colorWithRed:0.3 green:0.2 blue:0.1 alpha:1.0].CGColor;

    // 添加木纹纹理效果
    CAGradientLayer *woodGrain = [CAGradientLayer layer];
    woodGrain.colors = @[
        (id)[UIColor colorWithRed:0.55 green:0.4 blue:0.25 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.45 green:0.3 blue:0.15 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.5 green:0.35 blue:0.2 alpha:1.0].CGColor
    ];
    woodGrain.startPoint = CGPointMake(0, 0);
    woodGrain.endPoint = CGPointMake(0, 1);
    woodGrain.cornerRadius = [CelraDesignSystem cornerRadiusL];
    [self.doorFrameView.layer insertSublayer:woodGrain atIndex:0];

    self.doorFrameView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.doorFrameView];

    // Door view - 更有个性的门设计
    self.doorView = [[UIView alloc] init];
    self.doorView.backgroundColor = self.character.doorColor;
    self.doorView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.doorView.layer.borderWidth = 3;
    self.doorView.layer.borderColor = [UIColor colorWithRed:0.2 green:0.15 blue:0.1 alpha:1.0].CGColor;

    // 添加门的装饰性渐变
    CAGradientLayer *doorGradient = [CAGradientLayer layer];
    doorGradient.colors = @[
        (id)self.character.doorColor.CGColor,
        (id)[self.character.doorColor colorWithAlphaComponent:0.8].CGColor,
        (id)self.character.doorColor.CGColor
    ];
    doorGradient.startPoint = CGPointMake(0, 0);
    doorGradient.endPoint = CGPointMake(1, 1);
    doorGradient.cornerRadius = [CelraDesignSystem cornerRadiusM];
    [self.doorView.layer insertSublayer:doorGradient atIndex:0];

    self.doorView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.doorView];
    
    // Door handle - 更精致的门把手设计
    self.doorHandleView = [[UIView alloc] init];
    self.doorHandleView.backgroundColor = [UIColor colorWithRed:0.9 green:0.8 blue:0.4 alpha:1.0]; // 更亮的黄铜色
    self.doorHandleView.layer.cornerRadius = 8;
    self.doorHandleView.layer.borderWidth = 1;
    self.doorHandleView.layer.borderColor = [UIColor colorWithRed:0.7 green:0.6 blue:0.2 alpha:1.0].CGColor;

    // 添加金属光泽效果
    CAGradientLayer *handleGradient = [CAGradientLayer layer];
    handleGradient.colors = @[
        (id)[UIColor colorWithRed:1.0 green:0.9 blue:0.5 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.8 green:0.7 blue:0.3 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.9 green:0.8 blue:0.4 alpha:1.0].CGColor
    ];
    handleGradient.startPoint = CGPointMake(0, 0);
    handleGradient.endPoint = CGPointMake(1, 1);
    handleGradient.cornerRadius = 8;
    [self.doorHandleView.layer insertSublayer:handleGradient atIndex:0];

    self.doorHandleView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorView addSubview:self.doorHandleView];

    // Peephole - 更有科技感的猫眼
    self.peepholeView = [[UIView alloc] init];
    self.peepholeView.backgroundColor = [UIColor colorWithRed:0.05 green:0.05 blue:0.1 alpha:1.0];
    self.peepholeView.layer.cornerRadius = 12;
    self.peepholeView.layer.borderWidth = 3;
    self.peepholeView.layer.borderColor = [UIColor colorWithRed:0.7 green:0.6 blue:0.3 alpha:1.0].CGColor;

    // 添加内圈装饰
    UIView *innerRing = [[UIView alloc] init];
    innerRing.backgroundColor = [UIColor clearColor];
    innerRing.layer.cornerRadius = 8;
    innerRing.layer.borderWidth = 1;
    innerRing.layer.borderColor = [UIColor colorWithRed:0.5 green:0.4 blue:0.2 alpha:0.6].CGColor;
    innerRing.translatesAutoresizingMaskIntoConstraints = NO;
    [self.peepholeView addSubview:innerRing];

    self.peepholeView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorView addSubview:self.peepholeView];

    // Add AI indicator in peephole - 更有活力的指示器
    UIView *aiIndicator = [[UIView alloc] init];
    aiIndicator.backgroundColor = self.character.accentColor;
    aiIndicator.layer.cornerRadius = 4;

    // 添加脉动动画效果
    CABasicAnimation *pulseAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
    pulseAnimation.fromValue = @0.3;
    pulseAnimation.toValue = @1.0;
    pulseAnimation.duration = 1.5;
    pulseAnimation.repeatCount = INFINITY;
    pulseAnimation.autoreverses = YES;
    [aiIndicator.layer addAnimation:pulseAnimation forKey:@"pulse"];

    aiIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    [self.peepholeView addSubview:aiIndicator];

    [NSLayoutConstraint activateConstraints:@[
        // Inner ring constraints
        [innerRing.centerXAnchor constraintEqualToAnchor:self.peepholeView.centerXAnchor],
        [innerRing.centerYAnchor constraintEqualToAnchor:self.peepholeView.centerYAnchor],
        [innerRing.widthAnchor constraintEqualToConstant:16],
        [innerRing.heightAnchor constraintEqualToConstant:16],

        // AI indicator constraints
        [aiIndicator.centerXAnchor constraintEqualToAnchor:self.peepholeView.centerXAnchor],
        [aiIndicator.centerYAnchor constraintEqualToAnchor:self.peepholeView.centerYAnchor],
        [aiIndicator.widthAnchor constraintEqualToConstant:8],
        [aiIndicator.heightAnchor constraintEqualToConstant:8]
    ]];
    
    // Character name label - 更有个性的中文名称
    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.text = self.character.chineseName;
    self.nameLabel.font = [UIFont systemFontOfSize:22 weight:UIFontWeightBold];
    self.nameLabel.textColor = [CelraDesignSystem softYellowColor];
    self.nameLabel.textAlignment = NSTextAlignmentCenter;

    // 添加文字阴影效果
    self.nameLabel.layer.shadowColor = [UIColor blackColor].CGColor;
    self.nameLabel.layer.shadowOffset = CGSizeMake(1, 1);
    self.nameLabel.layer.shadowRadius = 2;
    self.nameLabel.layer.shadowOpacity = 0.5;

    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.nameLabel];

    // English name label - 更有风格的英文名称
    self.englishNameLabel = [[UILabel alloc] init];
    self.englishNameLabel.text = self.character.englishName;
    self.englishNameLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.englishNameLabel.textColor = self.character.accentColor;
    self.englishNameLabel.textAlignment = NSTextAlignmentCenter;
    self.englishNameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.englishNameLabel];

    // Description label - 更易读的描述文字
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.text = self.character.shortDescription;
    self.descriptionLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    self.descriptionLabel.textColor = [CelraDesignSystem softYellowColor];
    self.descriptionLabel.textAlignment = NSTextAlignmentCenter;
    self.descriptionLabel.numberOfLines = 3;
    self.descriptionLabel.alpha = 0.9;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.descriptionLabel];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Door frame - 更大更突出的门框
        [self.doorFrameView.topAnchor constraintEqualToAnchor:self.topAnchor
                                                     constant:[CelraDesignSystem spacingL]],
        [self.doorFrameView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.doorFrameView.widthAnchor constraintEqualToConstant:220],
        [self.doorFrameView.heightAnchor constraintEqualToConstant:280],
        
        // Door
        [self.doorView.topAnchor constraintEqualToAnchor:self.doorFrameView.topAnchor 
                                                constant:[CelraDesignSystem spacingS]],
        [self.doorView.leadingAnchor constraintEqualToAnchor:self.doorFrameView.leadingAnchor 
                                                    constant:[CelraDesignSystem spacingS]],
        [self.doorView.trailingAnchor constraintEqualToAnchor:self.doorFrameView.trailingAnchor 
                                                     constant:-[CelraDesignSystem spacingS]],
        [self.doorView.bottomAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor 
                                                   constant:-[CelraDesignSystem spacingS]],
        
        // Door handle - 更大更明显的门把手
        [self.doorHandleView.trailingAnchor constraintEqualToAnchor:self.doorView.trailingAnchor
                                                           constant:-[CelraDesignSystem spacingL]],
        [self.doorHandleView.centerYAnchor constraintEqualToAnchor:self.doorView.centerYAnchor],
        [self.doorHandleView.widthAnchor constraintEqualToConstant:16],
        [self.doorHandleView.heightAnchor constraintEqualToConstant:32],

        // Peephole - 更大更突出的猫眼
        [self.peepholeView.centerXAnchor constraintEqualToAnchor:self.doorView.centerXAnchor],
        [self.peepholeView.topAnchor constraintEqualToAnchor:self.doorView.topAnchor
                                                    constant:[CelraDesignSystem spacingXL] + 8],
        [self.peepholeView.widthAnchor constraintEqualToConstant:24],
        [self.peepholeView.heightAnchor constraintEqualToConstant:24],
        
        // Name label
        [self.nameLabel.topAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor 
                                                 constant:[CelraDesignSystem spacingM]],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.nameLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                      constant:-[CelraDesignSystem spacingS]],
        
        // English name label
        [self.englishNameLabel.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor 
                                                        constant:[CelraDesignSystem spacingXS]],
        [self.englishNameLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                            constant:[CelraDesignSystem spacingS]],
        [self.englishNameLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                             constant:-[CelraDesignSystem spacingS]],
        
        // Description label
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.englishNameLabel.bottomAnchor 
                                                        constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                            constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                             constant:-[CelraDesignSystem spacingS]],
        [self.descriptionLabel.bottomAnchor constraintLessThanOrEqualToAnchor:self.bottomAnchor 
                                                                     constant:-[CelraDesignSystem spacingM]]
    ]];
}

@end
